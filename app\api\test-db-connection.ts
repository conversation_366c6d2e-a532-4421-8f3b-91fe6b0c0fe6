import { NextApiRequest, NextApiResponse } from 'next';
import mysql from 'mysql2/promise';

// إعداد الاتصال بقاعدة البيانات
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'droobhajer_db',
  charset: 'utf8mb4'
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    });
  }

  let connection;

  try {
    console.log('🔄 اختبار الاتصال بقاعدة البيانات...');
    console.log('📊 إعدادات قاعدة البيانات:', {
      host: dbConfig.host,
      user: dbConfig.user,
      database: dbConfig.database,
      hasPassword: !!dbConfig.password
    });

    // محاولة الاتصال
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // اختبار الجداول الموجودة
    const [tables] = await connection.execute(`
      SELECT TABLE_NAME 
      FROM information_schema.tables 
      WHERE table_schema = ?
    `, [dbConfig.database]);

    console.log('📋 الجداول الموجودة:', tables);

    // اختبار جدول المنتجات
    let productsTest = null;
    try {
      const [productsResult] = await connection.execute(`
        SELECT COUNT(*) as count FROM products LIMIT 1
      `);
      productsTest = {
        exists: true,
        count: productsResult[0].count
      };
      console.log('📦 جدول المنتجات:', productsTest);
    } catch (error) {
      productsTest = {
        exists: false,
        error: error.message
      };
      console.log('❌ جدول المنتجات غير موجود:', error.message);
    }

    // اختبار جدول الفئات
    let categoriesTest = null;
    try {
      const [categoriesResult] = await connection.execute(`
        SELECT COUNT(*) as count FROM categories LIMIT 1
      `);
      categoriesTest = {
        exists: true,
        count: categoriesResult[0].count
      };
      console.log('📁 جدول الفئات:', categoriesTest);
    } catch (error) {
      categoriesTest = {
        exists: false,
        error: error.message
      };
      console.log('❌ جدول الفئات غير موجود:', error.message);
    }

    // اختبار جدول المديرين
    let adminsTest = null;
    try {
      const [adminsResult] = await connection.execute(`
        SELECT COUNT(*) as count FROM admins LIMIT 1
      `);
      adminsTest = {
        exists: true,
        count: adminsResult[0].count
      };
      console.log('👥 جدول المديرين:', adminsTest);
    } catch (error) {
      adminsTest = {
        exists: false,
        error: error.message
      };
      console.log('❌ جدول المديرين غير موجود:', error.message);
    }

    res.status(200).json({
      success: true,
      message: 'Database connection successful',
      messageAr: 'تم الاتصال بقاعدة البيانات بنجاح',
      data: {
        config: {
          host: dbConfig.host,
          user: dbConfig.user,
          database: dbConfig.database,
          hasPassword: !!dbConfig.password
        },
        tables: tables,
        tests: {
          products: productsTest,
          categories: categoriesTest,
          admins: adminsTest
        }
      }
    });

  } catch (error) {
    console.error('❌ خطأ في الاتصال بقاعدة البيانات:', error);
    
    res.status(500).json({
      success: false,
      message: 'Database connection failed',
      messageAr: 'فشل في الاتصال بقاعدة البيانات',
      error: {
        message: error.message,
        code: error.code,
        errno: error.errno,
        sqlState: error.sqlState
      },
      config: {
        host: dbConfig.host,
        user: dbConfig.user,
        database: dbConfig.database,
        hasPassword: !!dbConfig.password
      }
    });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}
