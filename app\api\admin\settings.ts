import { NextApiRequest, NextApiResponse } from 'next';
import { getSettings, updateSettings } from '../../../utils/database';

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    switch (req.method) {
      case 'GET':
        const settings = getSettings();
        res.status(200).json({ success: true, data: settings });
        break;

      case 'PUT':
        const updatedSettings = updateSettings(req.body);
        res.status(200).json({ success: true, data: updatedSettings });
        break;

      default:
        res.setHeader('Allow', ['GET', 'PUT']);
        res.status(405).json({ 
          success: false, 
          message: `Method ${req.method} not allowed` 
        });
    }
  } catch (error) {
    console.error('Settings API error:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error' 
    });
  }
}
