import { NextApiRequest, NextApiResponse } from 'next';
import {
  getProductsWithDetails,
  getFeaturedProducts,
  getProductsByCategoryWithDetails,
  getProductsBySubcategoryWithDetails
} from '../../../lib/mysql-database';
import { ProductWithDetails } from '../../../types/mysql-database';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    switch (req.method) {
      case 'GET':
        const { featured, categoryId, subcategoryId } = req.query;

        let products: ProductWithDetails[];

        if (featured === 'true') {
          products = await getFeaturedProducts();
        } else if (categoryId && typeof categoryId === 'string') {
          products = await getProductsByCategoryWithDetails(categoryId);
        } else if (subcategoryId && typeof subcategoryId === 'string') {
          products = await getProductsBySubcategoryWithDetails(subcategoryId);
        } else {
          products = await getProductsWithDetails();
        }

        res.status(200).json(products);
        break;

      default:
        res.setHeader('Allow', ['GET']);
        res.status(405).end(`Method ${req.method} Not Allowed`);
    }
  } catch (error) {
    console.error('API Error:', error);
    res.status(500).json({ error: 'Internal Server Error' });
  }
}
