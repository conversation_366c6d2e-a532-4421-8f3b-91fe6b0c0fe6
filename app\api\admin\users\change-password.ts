import { NextApiRequest, NextApiResponse } from 'next';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { executeQuery, executeQuerySingle } from '../../../../lib/database-config';

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';

interface AdminUser {
  id: number;
  username: string;
  email: string;
  password_hash: string;
  is_active: boolean;
}

// التحقق من صحة JWT token
function verifyToken(token: string) {
  try {
    return jwt.verify(token, JWT_SECRET) as any;
  } catch (error) {
    return null;
  }
}

// استخراج token من الطلب
function extractToken(req: NextApiRequest): string | null {
  const authHeader = req.headers.authorization;
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }
  
  const tokenFromCookie = req.cookies.authToken;
  if (tokenFromCookie) {
    return tokenFromCookie;
  }
  
  return null;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'PUT') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // التحقق من المصادقة
    const token = extractToken(req);
    if (!token) {
      return res.status(401).json({
        message: 'غير مصرح لك بالوصول',
        success: false
      });
    }

    const decoded = verifyToken(token);
    if (!decoded) {
      return res.status(401).json({
        message: 'رمز المصادقة غير صحيح',
        success: false
      });
    }

    const { userId, currentPassword, newPassword } = req.body;

    // التحقق من وجود البيانات المطلوبة
    if (!userId || !currentPassword || !newPassword) {
      return res.status(400).json({ 
        message: 'جميع الحقول مطلوبة',
        success: false 
      });
    }

    // التحقق من قوة كلمة المرور الجديدة
    if (newPassword.length < 6) {
      return res.status(400).json({ 
        message: 'كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل',
        success: false 
      });
    }

    // الحصول على بيانات المستخدم
    const user = await executeQuerySingle<AdminUser>(
      'SELECT id, username, email, password_hash, is_active FROM admins WHERE id = ? AND deleted_at IS NULL',
      [userId]
    );

    if (!user) {
      return res.status(404).json({ 
        message: 'المستخدم غير موجود',
        success: false 
      });
    }

    // التحقق من كلمة المرور الحالية
    const isValidCurrentPassword = await bcrypt.compare(currentPassword, user.password_hash);
    
    if (!isValidCurrentPassword) {
      return res.status(400).json({ 
        message: 'كلمة المرور الحالية غير صحيحة',
        success: false 
      });
    }

    // التحقق من أن كلمة المرور الجديدة مختلفة عن الحالية
    const isSamePassword = await bcrypt.compare(newPassword, user.password_hash);
    
    if (isSamePassword) {
      return res.status(400).json({ 
        message: 'كلمة المرور الجديدة يجب أن تكون مختلفة عن الحالية',
        success: false 
      });
    }

    // تشفير كلمة المرور الجديدة
    const saltRounds = 12;
    const newPasswordHash = await bcrypt.hash(newPassword, saltRounds);

    // تحديث كلمة المرور في قاعدة البيانات
    await executeQuery(
      'UPDATE admins SET password_hash = ?, updated_at = NOW() WHERE id = ?',
      [newPasswordHash, userId]
    );

    return res.status(200).json({
      success: true,
      message: 'تم تغيير كلمة المرور بنجاح'
    });

  } catch (error) {
    console.error('Change password error:', error);
    return res.status(500).json({ 
      message: 'حدث خطأ في الخادم',
      success: false 
    });
  }
}
