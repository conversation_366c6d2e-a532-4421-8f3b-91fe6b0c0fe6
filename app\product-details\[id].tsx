import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Image from 'next/image';
import Navbar from '../../../components/Navbar';
import Footer from '../../../components/Footer';
import Link from 'next/link';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { GetServerSideProps } from 'next';
import { useTranslation } from 'next-i18next';
import { ProductWithDetails } from '../../types/mysql-database';

const ProductDetailsPage = () => {
  const { t } = useTranslation('common');
  const router = useRouter();
  const { id } = router.query;

  const [product, setProduct] = useState<ProductWithDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [showToast, setShowToast] = useState(false);
  const [activeTab, setActiveTab] = useState('description');

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (showToast) {
      timer = setTimeout(() => setShowToast(false), 3000);
    }
    return () => clearTimeout(timer);
  }, [showToast]);

  useEffect(() => {
    if (id && typeof id === 'string') {
      fetchProductDetails(id);
    }
  }, [id]);

  const fetchProductDetails = async (productId: string) => {
    try {
      setLoading(true);
      const response = await fetch(`/api/products/${productId}`);

      if (response.ok) {
        const productData = await response.json();
        setProduct(productData);
      } else {
        console.error('Product not found:', response.status);
        setProduct(null);
      }
    } catch (error) {
      console.error('Error fetching product details:', error);
      setProduct(null);
    } finally {
      setLoading(false);
    }
  };

  // إذا كان router لم يحمل بعد أو البيانات تحمل، انتظر
  if (router.isFallback || !router.isReady || loading) {
    return (
      <>
        <Navbar locale="ar" />
        <div className="container mx-auto py-20 text-center">
          <div className="max-w-md mx-auto">
            <i className="ri-loader-4-line text-6xl text-gray-400 mb-4 animate-spin"></i>
            <h2 className="text-2xl font-bold mb-4 text-gray-800">{t('productDetails.loading')}</h2>
          </div>
        </div>
        <Footer locale="ar" />
      </>
    );
  }

  type CartItem = {
    id: string;
    title: string;
    image: string;
    price: number;
    quantity: number;
  };

  const addToCart = () => {
    if (!product || !product.is_available) return;

    const cartItem: CartItem = {
      id: product.id,
      title: product.title_ar,
      image: product.images?.[0]?.image_url || '/api/placeholder?width=400&height=300&text=لا توجد صورة',
      price: product.price,
      quantity: quantity
    };

    // جلب السلة الحالية من localStorage
    const existingCart = localStorage.getItem('cart');
    const cart: CartItem[] = existingCart ? JSON.parse(existingCart) : [];

    // البحث عن المنتج في السلة
    const existingItemIndex = cart.findIndex((item: CartItem) => item.id === product.id);

    if (existingItemIndex > -1) {
      // إذا كان المنتج موجود، زيادة الكمية
      cart[existingItemIndex].quantity += quantity;
    } else {
      // إذا لم يكن موجود، إضافته للسلة
      cart.push(cartItem);
    }

    // حفظ السلة المحدثة
    localStorage.setItem('cart', JSON.stringify(cart));

    // إرسال حدث لتحديث عداد السلة
    window.dispatchEvent(new Event('cartUpdated'));

    setShowToast(true);
  };

  if (!product) {
    return (
      <>
        <Navbar locale="ar" />
        <div className="container mx-auto py-20 text-center">
          <div className="max-w-md mx-auto">
            <i className="ri-error-warning-line text-6xl text-gray-400 mb-4"></i>
            <h2 className="text-2xl font-bold mb-4 text-gray-800">{t('productDetails.notFound')}</h2>
            <p className="text-gray-600 mb-6">{t('productDetails.notFoundMessage')}</p>
            <Link href="/products" className="bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary/90 transition-colors inline-flex items-center gap-2">
              <i className="ri-arrow-right-line"></i>
              {t('productDetails.backToProducts')}
            </Link>
          </div>
        </div>
        <Footer locale="ar" />
      </>
    );
  }

  return (
    <>
      <Navbar locale="ar" />
      {showToast && (
        <div className="fixed top-20 left-1/2 transform -translate-x-1/2 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transition-all duration-300">
          <div className="flex items-center gap-2">
            <i className="ri-check-line"></i>
            {t('productDetails.addedToCart', { product: product.title })}
          </div>
        </div>
      )}

      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <nav className="flex items-center gap-2 text-sm text-gray-600 mb-8">
          <Link href="/" className="hover:text-primary">{t('productDetails.home')}</Link>
          <i className="ri-arrow-left-s-line"></i>
          <Link href="/products" className="hover:text-primary">{t('productDetails.products')}</Link>
          <i className="ri-arrow-left-s-line"></i>
          <span className="text-gray-800">{product.title}</span>
        </nav>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-12">
          {/* معرض الصور */}
          <div className="space-y-4">
            <div className="relative">
              <Image
                src={product.images?.[selectedImageIndex]?.image_url || '/api/placeholder?width=800&height=400&text=لا توجد صورة'}
                alt={product.title_ar}
                width={800}
                height={384}
                className="w-full h-96 object-cover transition-transform duration-300 hover:scale-105 rounded-lg"
                onError={(e) => {
                  (e.target as HTMLImageElement).src = '/api/placeholder?width=800&height=400&text=خطأ في الصورة';
                }}
              />

              {!product.is_available && (
                <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-lg">
                  <span className="bg-red-500 text-white px-4 py-2 rounded-lg font-bold">{t('productDetails.unavailable')}</span>
                </div>
              )}
            </div>
            {product.images && product.images.length > 1 && (
              <div className="flex gap-2 overflow-x-auto">
                {product.images.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedImageIndex(index)}
                    className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 transition-all duration-200 ${
                      selectedImageIndex === index ? 'border-primary' : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <Image
                      src={image.image_url || '/api/placeholder?width=80&height=80&text=صورة'}
                      alt={`${product.title_ar} ${index + 1}`}
                      width={80}
                      height={80}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        (e.target as HTMLImageElement).src = '/api/placeholder?width=80&height=80&text=خطأ';
                      }}
                    />
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* معلومات المنتج */}
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl lg:text-4xl font-bold text-gray-800 mb-2">{product.title_ar}</h1>
              <div className="flex items-center gap-2 mb-4">
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                  product.is_available ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {product.is_available ? t('productDetails.available') : t('productDetails.unavailable')}
                </span>
              </div>
            </div>

            <div className="flex items-center gap-4">
              <span className="text-3xl font-bold text-primary">{product.price} {t('cart.currency')}</span>
            </div>

            <p className="text-gray-700 leading-relaxed">{product.description_ar}</p>

            {/* كمية المنتج */}
            <div className="flex items-center gap-4">
              <span className="font-medium text-gray-700">{t('productDetails.quantity')}</span>
              <div className="flex items-center border border-gray-300 rounded-lg">
                <button
                  onClick={() => setQuantity(Math.max(1, quantity - 1))}
                  className="px-3 py-2 hover:bg-gray-100 transition-colors"
                >
                  <i className="ri-subtract-line"></i>
                </button>
                <span className="px-4 py-2 border-x border-gray-300 min-w-[60px] text-center">{quantity}</span>
                <button
                  onClick={() => setQuantity(quantity + 1)}
                  className="px-3 py-2 hover:bg-gray-100 transition-colors"
                >
                  <i className="ri-add-line"></i>
                </button>
              </div>
            </div>

            {/* أزرار الإجراءات */}
            <div className="flex flex-col sm:flex-row gap-4">
              <button
                onClick={addToCart}
                disabled={!product.is_available}
                className={`flex-1 px-6 py-3 rounded-lg font-semibold transition-all duration-300 flex items-center justify-center gap-2 ${
                  product.is_available
                    ? 'bg-primary text-white hover:bg-primary/90 hover:shadow-lg transform hover:-translate-y-0.5'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
              >
                <i className="ri-shopping-cart-2-line"></i>
                {t('productDetails.addToCart')}
              </button>
              <a
                href={`https://wa.me/966501234567?text=${encodeURIComponent('مرحباً! كيف يمكننا مساعدتك اليوم؟\n\nأريد الاستفسار عن هذا المنتج:\n\n*' + product.title_ar + '*\n\n' + t('productDetails.price') + ': ' + product.price + ' ' + t('cart.currency') + '\n\n' + product.description_ar + '\n\nرابط المنتج:\n' + window.location.origin + '/product-details/' + product.id)}`}
                target="_blank"
                rel="noopener noreferrer"
                className="px-6 py-3 bg-green-500 text-white rounded-lg font-semibold hover:bg-green-600 transition-all duration-300 flex items-center justify-center gap-2 hover:shadow-lg transform hover:-translate-y-0.5"
              >
                <i className="ri-whatsapp-line"></i>
                {t('productDetails.whatsapp')}
              </a>
            </div>
          </div>
        </div>

        {/* التبويبات */}
        <div className="bg-white rounded-xl shadow-lg overflow-hidden">
          <div className="flex border-b border-gray-200">
            <button
              onClick={() => setActiveTab('description')}
              className={`px-6 py-4 font-semibold transition-colors ${
                activeTab === 'description'
                  ? 'text-primary border-b-2 border-primary bg-primary/5'
                  : 'text-gray-600 hover:text-primary'
              }`}
            >
              {t('productDetails.detailedDescription')}
            </button>
            <button
              onClick={() => setActiveTab('features')}
              className={`px-6 py-4 font-semibold transition-colors ${
                activeTab === 'features'
                  ? 'text-primary border-b-2 border-primary bg-primary/5'
                  : 'text-gray-600 hover:text-primary'
              }`}
            >
              {t('productDetails.features')}
            </button>
            <button
              onClick={() => setActiveTab('specifications')}
              className={`px-6 py-4 font-semibold transition-colors ${
                activeTab === 'specifications'
                  ? 'text-primary border-b-2 border-primary bg-primary/5'
                  : 'text-gray-600 hover:text-primary'
              }`}
            >
              {t('productDetails.specifications')}
            </button>
          </div>

          <div className="p-6">
            {activeTab === 'description' && (
              <div className="prose prose-lg max-w-none">
                <p className="text-gray-700 leading-relaxed text-lg">{product.description_ar}</p>
              </div>
            )}

            {activeTab === 'features' && (
              <div className="space-y-4">
                <h3 className="text-xl font-bold text-gray-800 mb-4">{t('productDetails.productFeatures')}</h3>
                {product.features && product.features.length > 0 ? (
                  <ul className="space-y-3">
                    {product.features.map((feature, index) => (
                      <li key={index} className="flex items-start gap-3">
                        <i className="ri-check-line text-green-500 text-xl mt-0.5"></i>
                        <span className="text-gray-700">{feature.feature_text_ar}</span>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p className="text-gray-600">لا توجد مميزات محددة لهذا المنتج</p>
                )}
              </div>
            )}

            {activeTab === 'specifications' && (
              <div className="space-y-4">
                <h3 className="text-xl font-bold text-gray-800 mb-4">{t('productDetails.technicalSpecs')}</h3>
                {product.specifications && product.specifications.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {product.specifications.map((spec, index) => (
                      <div key={index} className="flex justify-between items-center p-4 bg-gray-50 rounded-lg">
                        <span className="font-medium text-gray-700">{spec.spec_key_ar}:</span>
                        <span className="text-gray-600">{spec.spec_value_ar}</span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-600">لا توجد مواصفات محددة لهذا المنتج</p>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
      <Footer locale="ar" />
    </>
  );
};

export const getServerSideProps: GetServerSideProps = async ({ locale }) => {
  return {
    props: {
      ...(await serverSideTranslations(locale ?? 'ar', ['common'])),
    },
  };
};

export default ProductDetailsPage;
