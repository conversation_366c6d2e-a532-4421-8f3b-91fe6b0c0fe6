import { NextApiRequest, NextApiResponse } from 'next';
import formidable from 'formidable';
import fs from 'fs';
import path from 'path';
import crypto from 'crypto';

export const config = {
  api: {
    bodyParser: false,
  },
};

// أنواع الملفات المسموحة
const ALLOWED_MIME_TYPES = [
  'image/jpeg',
  'image/jpg', 
  'image/png',
  'image/webp',
  'image/gif'
];

// امتدادات الملفات المسموحة
const ALLOWED_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.webp', '.gif'];

// الحد الأقصى لحجم الملف (5MB)
const MAX_FILE_SIZE = 5 * 1024 * 1024;

// إنشاء اسم ملف فريد
function generateUniqueFilename(originalFilename: string): string {
  const ext = path.extname(originalFilename).toLowerCase();
  const timestamp = Date.now();
  const randomString = crypto.randomBytes(8).toString('hex');
  return `${timestamp}_${randomString}${ext}`;
}

// التحقق من نوع الملف
function validateFile(file: formidable.File): { valid: boolean; error?: string } {
  // فحص MIME type
  if (!file.mimetype || !ALLOWED_MIME_TYPES.includes(file.mimetype)) {
    return { valid: false, error: 'نوع الملف غير مدعوم' };
  }

  // فحص الامتداد
  if (!file.originalFilename) {
    return { valid: false, error: 'اسم الملف غير صحيح' };
  }

  const ext = path.extname(file.originalFilename).toLowerCase();
  if (!ALLOWED_EXTENSIONS.includes(ext)) {
    return { valid: false, error: 'امتداد الملف غير مدعوم' };
  }

  // فحص الحجم
  if (file.size > MAX_FILE_SIZE) {
    return { valid: false, error: 'حجم الملف كبير جداً (الحد الأقصى 5MB)' };
  }

  return { valid: true };
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ 
      success: false,
      error: 'Method not allowed',
      messageAr: 'الطريقة غير مسموحة'
    });
  }

  try {
    // إنشاء مجلد uploads إذا لم يكن موجوداً
    const uploadsDir = path.join(process.cwd(), 'public', 'uploads');
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
    }

    // إعداد formidable
    const form = formidable({
      uploadDir: uploadsDir,
      keepExtensions: false,
      maxFileSize: MAX_FILE_SIZE,
      filter: (part) => {
        return !!(part.mimetype && ALLOWED_MIME_TYPES.includes(part.mimetype));
      },
    });

    // تحليل الطلب
    const [fields, files] = await form.parse(req);

    const uploadedFiles: string[] = [];
    const errors: string[] = [];

    // معالجة الملفات المرفوعة
    for (const [fieldName, fileArray] of Object.entries(files)) {
      const filesToProcess = Array.isArray(fileArray) ? fileArray : [fileArray];

      for (const file of filesToProcess) {
        if (file && file.filepath && file.originalFilename) {
          // التحقق من صحة الملف
          const validation = validateFile(file);
          if (!validation.valid) {
            errors.push(`${file.originalFilename}: ${validation.error}`);
            // حذف الملف المؤقت
            try {
              fs.unlinkSync(file.filepath);
            } catch (e) {
              console.error('Error deleting temp file:', e);
            }
            continue;
          }

          try {
            // إنشاء اسم ملف فريد
            const uniqueFilename = generateUniqueFilename(file.originalFilename);
            const finalPath = path.join(uploadsDir, uniqueFilename);

            // نقل الملف إلى المكان النهائي
            fs.renameSync(file.filepath, finalPath);

            // إضافة رابط الملف
            uploadedFiles.push(`/uploads/${uniqueFilename}`);

            console.log(`✅ تم رفع الملف: ${uniqueFilename}`);
          } catch (error) {
            console.error('Error processing file:', error);
            errors.push(`فشل في حفظ الملف: ${file.originalFilename}`);
            
            // تنظيف الملف المؤقت
            try {
              if (fs.existsSync(file.filepath)) {
                fs.unlinkSync(file.filepath);
              }
            } catch (e) {
              console.error('Error cleaning up temp file:', e);
            }
          }
        }
      }
    }

    // إرجاع النتائج
    if (uploadedFiles.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'No valid files uploaded',
        errors: errors,
        messageAr: 'لم يتم رفع أي ملفات صحيحة'
      });
    }

    res.status(200).json({
      success: true,
      files: uploadedFiles,
      errors: errors.length > 0 ? errors : undefined,
      message: `تم رفع ${uploadedFiles.length} ملف بنجاح`,
      messageAr: `تم رفع ${uploadedFiles.length} ملف بنجاح${errors.length > 0 ? ` مع ${errors.length} تحذيرات` : ''}`
    });

  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({ 
      success: false,
      error: 'Failed to upload files',
      messageAr: 'فشل في رفع الملفات',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
