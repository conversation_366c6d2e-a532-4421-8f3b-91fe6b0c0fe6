import { NextApiRequest, NextApiResponse } from 'next';
import jwt from 'jsonwebtoken';
import { executeQuery, executeQuerySingle } from '../../../../lib/database-config';

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';

// التحقق من صحة JWT token
function verifyToken(token: string) {
  try {
    return jwt.verify(token, JWT_SECRET) as any;
  } catch (error) {
    return null;
  }
}

// استخراج token من الطلب
function extractToken(req: NextApiRequest): string | null {
  const authHeader = req.headers.authorization;
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }
  
  const tokenFromCookie = req.cookies.authToken;
  if (tokenFromCookie) {
    return tokenFromCookie;
  }
  
  return null;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'DELETE') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // التحقق من المصادقة
    const token = extractToken(req);
    if (!token) {
      return res.status(401).json({ 
        message: 'غير مصرح لك بالوصول',
        success: false 
      });
    }

    const decoded = verifyToken(token);
    if (!decoded) {
      return res.status(401).json({ 
        message: 'رمز المصادقة غير صحيح',
        success: false 
      });
    }

    const { userId } = req.body;

    // التحقق من وجود معرف المستخدم
    if (!userId) {
      return res.status(400).json({ 
        message: 'معرف المستخدم مطلوب',
        success: false 
      });
    }

    // التحقق من أن المستخدم لا يحذف نفسه
    if (decoded.userId === userId) {
      return res.status(400).json({ 
        message: 'لا يمكنك حذف حسابك الخاص',
        success: false 
      });
    }

    // التحقق من وجود المستخدم
    const existingUser = await executeQuerySingle(
      'SELECT id, username FROM admins WHERE id = ? AND deleted_at IS NULL',
      [userId]
    );

    if (!existingUser) {
      return res.status(404).json({ 
        message: 'المستخدم غير موجود',
        success: false 
      });
    }

    // حذف ناعم - تحديث deleted_at
    await executeQuery(
      'UPDATE admins SET deleted_at = NOW(), updated_at = NOW() WHERE id = ?',
      [userId]
    );

    return res.status(200).json({
      success: true,
      message: 'تم حذف المستخدم بنجاح'
    });

  } catch (error) {
    console.error('Delete user error:', error);
    return res.status(500).json({ 
      message: 'حدث خطأ في الخادم',
      success: false 
    });
  }
}
