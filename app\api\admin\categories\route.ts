import { NextRequest, NextResponse } from 'next/server';
import { getCategories, addCategory, updateCategory, deleteCategory, getCategoryById } from '../../../../lib/mysql-database';
import { requireAdminAuth } from '../../../../lib/auth';
import { v4 as uuidv4 } from 'uuid';

// GET - جلب جميع الفئات
export async function GET(request: NextRequest) {
  try {
    // TODO: إعادة تفعيل المصادقة لاحقاً
    // const user = requireAdminAuth(request);
    // if (!user) {
    //   return NextResponse.json({
    //     success: false,
    //     message: 'Authentication required',
    //     messageAr: 'المصادقة مطلوبة'
    //   }, { status: 401 });
    // }

    const categories = await getCategories();
    return NextResponse.json({ success: true, data: categories });

  } catch (error) {
    console.error('Categories GET API error:', error);
    return NextResponse.json({
      success: false,
      message: 'Internal server error'
    }, { status: 500 });
  }
}

// POST - إضافة فئة جديدة
export async function POST(request: NextRequest) {
  try {
    // TODO: إعادة تفعيل المصادقة لاحقاً
    // const user = requireAdminAuth(request);
    // if (!user) {
    //   return NextResponse.json({
    //     success: false,
    //     message: 'Authentication required',
    //     messageAr: 'المصادقة مطلوبة'
    //   }, { status: 401 });
    // }

    const body = await request.json();
    const { name, nameAr, description, descriptionAr, image, isActive } = body;

    if (!name || !nameAr) {
      return NextResponse.json({
        success: false,
        message: 'Name and Arabic name are required',
        messageAr: 'الاسم والاسم بالعربية مطلوبان'
      }, { status: 400 });
    }

    const categoryData = {
      id: uuidv4(),
      name,
      name_ar: nameAr,
      description: description || null,
      description_ar: descriptionAr || null,
      image: image || null,
      is_active: isActive !== undefined ? isActive : true
    };

    const newCategory = await addCategory(categoryData);
    return NextResponse.json({ success: true, data: newCategory }, { status: 201 });

  } catch (error) {
    console.error('Categories POST API error:', error);
    return NextResponse.json({
      success: false,
      message: 'Internal server error'
    }, { status: 500 });
  }
}

// PUT - تحديث فئة موجودة
export async function PUT(request: NextRequest) {
  try {
    // TODO: إعادة تفعيل المصادقة لاحقاً
    // const user = requireAdminAuth(request);
    // if (!user) {
    //   return NextResponse.json({
    //     success: false,
    //     message: 'Authentication required',
    //     messageAr: 'المصادقة مطلوبة'
    //   }, { status: 401 });
    // }

    const { searchParams } = new URL(request.url);
    const updateId = searchParams.get('id');

    if (!updateId) {
      return NextResponse.json({
        success: false,
        message: 'Category ID is required',
        messageAr: 'معرف الفئة مطلوب'
      }, { status: 400 });
    }

    const existingCategory = await getCategoryById(updateId);
    if (!existingCategory) {
      return NextResponse.json({
        success: false,
        message: 'Category not found',
        messageAr: 'الفئة غير موجودة'
      }, { status: 404 });
    }

    const body = await request.json();
    const updatedCategory = await updateCategory(updateId, body);
    return NextResponse.json({ success: true, data: updatedCategory });

  } catch (error) {
    console.error('Categories PUT API error:', error);
    return NextResponse.json({
      success: false,
      message: 'Internal server error'
    }, { status: 500 });
  }
}

// DELETE - حذف فئة
export async function DELETE(request: NextRequest) {
  try {
    // TODO: إعادة تفعيل المصادقة لاحقاً
    // const user = requireAdminAuth(request);
    // if (!user) {
    //   return NextResponse.json({
    //     success: false,
    //     message: 'Authentication required',
    //     messageAr: 'المصادقة مطلوبة'
    //   }, { status: 401 });
    // }

    const { searchParams } = new URL(request.url);
    const deleteId = searchParams.get('id');

    if (!deleteId) {
      return NextResponse.json({
        success: false,
        message: 'Category ID is required',
        messageAr: 'معرف الفئة مطلوب'
      }, { status: 400 });
    }

    const deleted = await deleteCategory(deleteId);

    if (!deleted) {
      return NextResponse.json({
        success: false,
        message: 'Category not found',
        messageAr: 'الفئة غير موجودة'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      message: 'Category deleted successfully',
      messageAr: 'تم حذف الفئة بنجاح'
    });

  } catch (error) {
    console.error('Categories DELETE API error:', error);
    return NextResponse.json({
      success: false,
      message: 'Internal server error'
    }, { status: 500 });
  }
}
