import { NextApiRequest, NextApiResponse } from 'next';
import mysql from 'mysql2/promise';

// إعداد الاتصال بقاعدة البيانات
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'droobhajer_db',
  charset: 'utf8mb4'
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    });
  }

  let connection;

  try {
    console.log('🔄 اختبار جلب الإحصائيات...');
    
    // إنشاء اتصال بقاعدة البيانات
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ تم الاتصال بقاعدة البيانات');

    // إحصائيات المنتجات
    console.log('📦 جلب إحصائيات المنتجات...');
    const [productsResult] = await connection.execute(`
      SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN is_active = 1 THEN 1 END) as active,
        COUNT(CASE WHEN is_featured = 1 THEN 1 END) as featured
      FROM products
    `);
    console.log('📦 نتيجة المنتجات:', productsResult[0]);

    // إحصائيات الفئات
    console.log('📁 جلب إحصائيات الفئات...');
    const [categoriesResult] = await connection.execute(`
      SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN is_active = 1 THEN 1 END) as active
      FROM categories
    `);
    console.log('📁 نتيجة الفئات:', categoriesResult[0]);

    // إحصائيات الفئات الفرعية
    console.log('📂 جلب إحصائيات الفئات الفرعية...');
    const [subcategoriesResult] = await connection.execute(`
      SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN is_active = 1 THEN 1 END) as active
      FROM subcategories
    `);
    console.log('📂 نتيجة الفئات الفرعية:', subcategoriesResult[0]);

    // إحصائيات المديرين
    console.log('👥 جلب إحصائيات المديرين...');
    const [adminsResult] = await connection.execute(`
      SELECT 
        COUNT(*) as total
      FROM admins
    `);
    console.log('👥 نتيجة المديرين:', adminsResult[0]);

    // تجميع النتائج
    const stats = {
      products: {
        total: productsResult[0].total,
        active: productsResult[0].active,
        featured: productsResult[0].featured
      },
      categories: {
        total: categoriesResult[0].total,
        active: categoriesResult[0].active
      },
      subcategories: {
        total: subcategoriesResult[0].total,
        active: subcategoriesResult[0].active
      },
      admins: {
        total: adminsResult[0].total
      }
    };

    console.log('✅ الإحصائيات النهائية:', stats);

    res.status(200).json({
      success: true,
      message: 'Stats fetched successfully',
      messageAr: 'تم جلب الإحصائيات بنجاح',
      stats
    });

  } catch (error) {
    console.error('❌ خطأ في جلب الإحصائيات:', error);
    
    res.status(500).json({
      success: false,
      message: 'Failed to fetch stats',
      messageAr: 'فشل في جلب الإحصائيات',
      error: {
        message: error.message,
        code: error.code,
        errno: error.errno
      }
    });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}
