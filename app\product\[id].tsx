import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Navbar from '../../components/Navbar';
import Footer from '../../components/Footer';
import { Product, Category, Subcategory } from '../../types/database';
import { productsApi, categoriesApi, subcategoriesApi } from '../../lib/api';
import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { GetStaticProps, GetStaticPaths } from 'next';

const ProductDetailPage = () => {
  const router = useRouter();
  const { id } = router.query;
  const { t } = useTranslation('common');
  
  const [product, setProduct] = useState<Product | null>(null);
  const [category, setCategory] = useState<Category | null>(null);
  const [subcategory, setSubcategory] = useState<Subcategory | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [showToast, setShowToast] = useState(false);

  useEffect(() => {
    if (id && typeof id === 'string') {
      fetchProductDetails(id);
    }
  }, [id]);

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (showToast) {
      timer = setTimeout(() => setShowToast(false), 3000);
    }
    return () => clearTimeout(timer);
  }, [showToast]);

  const fetchProductDetails = async (productId: string) => {
    try {
      setLoading(true);
      const productData = await productsApi.getById(productId);
      setProduct(productData);

      // جلب بيانات الفئة والفئة الفرعية
      if (productData.categoryId) {
        const categoryData = await categoriesApi.getById(productData.categoryId);
        setCategory(categoryData);
      }

      if (productData.subcategoryId) {
        const subcategoryData = await subcategoriesApi.getById(productData.subcategoryId);
        setSubcategory(subcategoryData);
      }
    } catch (error) {
      console.error('Error fetching product details:', error);
    } finally {
      setLoading(false);
    }
  };

  const addToCart = () => {
    if (!product || !product.available) return;

    const cartItem = {
      id: product.id,
      title: product.titleAr,
      image: product.images[0] || 'https://via.placeholder.com/400x300',
      price: product.price,
      quantity: quantity
    };

    // جلب السلة الحالية من localStorage
    const existingCart = localStorage.getItem('cart');
    let cart = existingCart ? JSON.parse(existingCart) : [];

    // البحث عن المنتج في السلة
    const existingItemIndex = cart.findIndex((item: any) => item.id === product.id);

    if (existingItemIndex > -1) {
      // إذا كان المنتج موجود، زيادة الكمية
      cart[existingItemIndex].quantity += quantity;
    } else {
      // إذا لم يكن موجود، إضافته للسلة
      cart.push(cartItem);
    }

    // حفظ السلة المحدثة
    localStorage.setItem('cart', JSON.stringify(cart));

    // إظهار رسالة التأكيد
    setShowToast(true);
  };

  if (loading) {
    return (
      <>
        <Navbar />
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل تفاصيل المنتج...</p>
          </div>
        </div>
        <Footer />
      </>
    );
  }

  if (!product) {
    return (
      <>
        <Navbar />
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-800 mb-4">المنتج غير موجود</h1>
            <button
              onClick={() => router.push('/products')}
              className="bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary/90 transition-colors"
            >
              العودة إلى المنتجات
            </button>
          </div>
        </div>
        <Footer />
      </>
    );
  }

  return (
    <>
      <Head>
        <title>{product.titleAr} - VidMeet</title>
        <meta name="description" content={product.descriptionAr} />
      </Head>

      <Navbar />
      
      <main className="min-h-screen bg-gray-50 py-8">
        <div className="container mx-auto px-4">
          {/* Breadcrumb */}
          <nav className="flex items-center space-x-2 space-x-reverse text-sm text-gray-600 mb-8">
            <a href="/" className="hover:text-primary">الرئيسية</a>
            <span>/</span>
            <a href="/products" className="hover:text-primary">المنتجات</a>
            {category && (
              <>
                <span>/</span>
                <span className="hover:text-primary">{category.nameAr}</span>
              </>
            )}
            {subcategory && (
              <>
                <span>/</span>
                <span className="hover:text-primary">{subcategory.nameAr}</span>
              </>
            )}
            <span>/</span>
            <span className="text-primary font-medium">{product.titleAr}</span>
          </nav>

          <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 p-8">
              {/* Product Images */}
              <div className="space-y-4">
                <div className="aspect-square rounded-xl overflow-hidden bg-gray-100">
                  <img
                    src={product.images[selectedImageIndex] || 'https://via.placeholder.com/600x600'}
                    alt={product.titleAr}
                    className="w-full h-full object-cover"
                  />
                </div>
                
                {product.images.length > 1 && (
                  <div className="flex space-x-2 space-x-reverse overflow-x-auto">
                    {product.images.map((image, index) => (
                      <button
                        key={index}
                        onClick={() => setSelectedImageIndex(index)}
                        className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 transition-colors ${
                          selectedImageIndex === index ? 'border-primary' : 'border-gray-200'
                        }`}
                      >
                        <img
                          src={image || 'https://via.placeholder.com/80x80'}
                          alt={`${product.titleAr} ${index + 1}`}
                          className="w-full h-full object-cover"
                        />
                      </button>
                    ))}
                  </div>
                )}
              </div>

              {/* Product Info */}
              <div className="space-y-6">
                <div>
                  <h1 className="text-3xl font-bold text-gray-800 mb-2">{product.titleAr}</h1>
                  <p className="text-lg text-gray-600">{product.title}</p>
                  
                  {/* Category & Subcategory */}
                  <div className="flex items-center space-x-2 space-x-reverse mt-4">
                    {category && (
                      <span className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">
                        {category.nameAr}
                      </span>
                    )}
                    {subcategory && (
                      <span className="px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full">
                        {subcategory.nameAr}
                      </span>
                    )}
                  </div>
                </div>

                {/* Price */}
                <div className="flex items-center space-x-4 space-x-reverse">
                  <span className="text-3xl font-bold text-primary">{product.price} ر.س</span>
                  {product.originalPrice && product.originalPrice > product.price && (
                    <span className="text-xl text-gray-500 line-through">{product.originalPrice} ر.س</span>
                  )}
                </div>

                {/* Availability */}
                <div className="flex items-center space-x-2 space-x-reverse">
                  <div className={`w-3 h-3 rounded-full ${product.available ? 'bg-green-500' : 'bg-red-500'}`}></div>
                  <span className={`font-medium ${product.available ? 'text-green-600' : 'text-red-600'}`}>
                    {product.available ? 'متوفر' : 'غير متوفر'}
                  </span>
                </div>

                {/* Description */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-3">الوصف</h3>
                  <p className="text-gray-600 leading-relaxed">{product.descriptionAr}</p>
                </div>

                {/* Features */}
                {product.featuresAr && product.featuresAr.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-3">المميزات</h3>
                    <ul className="space-y-2">
                      {product.featuresAr.map((feature, index) => (
                        <li key={index} className="flex items-center space-x-2 space-x-reverse">
                          <i className="ri-check-line text-green-500"></i>
                          <span className="text-gray-600">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Specifications */}
                {product.specificationsAr && Object.keys(product.specificationsAr).length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-3">المواصفات</h3>
                    <div className="bg-gray-50 rounded-lg p-4">
                      <dl className="space-y-2">
                        {Object.entries(product.specificationsAr).map(([key, value]) => (
                          <div key={key} className="flex justify-between">
                            <dt className="font-medium text-gray-700">{key}:</dt>
                            <dd className="text-gray-600">{value}</dd>
                          </div>
                        ))}
                      </dl>
                    </div>
                  </div>
                )}

                {/* Quantity Selector */}
                <div className="flex items-center space-x-4 space-x-reverse">
                  <span className="font-medium text-gray-700">الكمية:</span>
                  <div className="flex items-center border border-gray-300 rounded-lg">
                    <button
                      onClick={() => setQuantity(Math.max(1, quantity - 1))}
                      className="px-3 py-2 hover:bg-gray-100 transition-colors"
                    >
                      <i className="ri-subtract-line"></i>
                    </button>
                    <span className="px-4 py-2 border-x border-gray-300 min-w-[60px] text-center">{quantity}</span>
                    <button
                      onClick={() => setQuantity(quantity + 1)}
                      className="px-3 py-2 hover:bg-gray-100 transition-colors"
                    >
                      <i className="ri-add-line"></i>
                    </button>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="pt-6 border-t space-y-3">
                  <button
                    onClick={addToCart}
                    disabled={!product.available}
                    className={`w-full py-4 px-6 rounded-xl font-bold text-lg flex items-center justify-center space-x-2 space-x-reverse transition-all duration-300 ${
                      product.available
                        ? 'bg-primary hover:bg-primary/90 text-white hover:shadow-lg transform hover:-translate-y-0.5'
                        : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    }`}
                  >
                    <i className="ri-shopping-cart-2-line text-xl"></i>
                    <span>{product.available ? 'إضافة إلى السلة' : 'غير متوفر'}</span>
                  </button>

                  <a
                    href={`https://wa.me/966123456789?text=أريد الاستفسار عن ${product.titleAr}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-full bg-green-600 hover:bg-green-700 text-white py-4 px-6 rounded-xl font-bold text-lg flex items-center justify-center space-x-2 space-x-reverse transition-colors"
                  >
                    <i className="ri-whatsapp-line text-xl"></i>
                    <span>تواصل معنا عبر واتساب</span>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />

      {/* Toast Notification */}
      {showToast && (
        <div className="fixed bottom-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 flex items-center space-x-2 space-x-reverse animate-slide-up">
          <i className="ri-check-line text-xl"></i>
          <span>تم إضافة المنتج إلى السلة بنجاح!</span>
        </div>
      )}
    </>
  );
};

export const getStaticPaths: GetStaticPaths = async () => {
  return {
    paths: [],
    fallback: 'blocking'
  };
};

export const getStaticProps: GetStaticProps = async ({ locale }) => {
  return {
    props: {
      ...(await serverSideTranslations(locale ?? 'ar', ['common'])),
    },
  };
};

export default ProductDetailPage;
