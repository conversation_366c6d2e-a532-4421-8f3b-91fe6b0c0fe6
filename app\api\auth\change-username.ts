import { NextApiRequest, NextApiResponse } from 'next';
import { requireAdminAuth } from '../../../lib/auth';
import { validateAdminLogin } from '../../../lib/secure-storage';
import fs from 'fs';
import path from 'path';

// مسار ملف قاعدة البيانات
const DB_PATH = path.join(process.cwd(), 'src/data/database.json');

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // السماح فقط بطلبات POST
  if (req.method !== 'POST') {
    return res.status(405).json({ 
      success: false, 
      message: 'Method not allowed' 
    });
  }

  try {
    // التحقق من المصادقة
    const user = requireAdminAuth(req);
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required',
        messageAr: 'المصادقة مطلوبة'
      });
    }

    const { currentPassword, newUsername } = req.body;

    // التحقق من وجود البيانات المطلوبة
    if (!currentPassword || !newUsername) {
      return res.status(400).json({
        success: false,
        message: 'Current password and new username are required',
        messageAr: 'كلمة المرور الحالية واسم المستخدم الجديد مطلوبان'
      });
    }

    // التحقق من طول اسم المستخدم الجديد
    if (newUsername.length < 3) {
      return res.status(400).json({
        success: false,
        message: 'New username must be at least 3 characters long',
        messageAr: 'اسم المستخدم الجديد يجب أن يكون 3 أحرف على الأقل'
      });
    }

    // التحقق من صحة اسم المستخدم (أحرف وأرقام فقط)
    const usernameRegex = /^[a-zA-Z0-9_]+$/;
    if (!usernameRegex.test(newUsername)) {
      return res.status(400).json({
        success: false,
        message: 'Username can only contain letters, numbers, and underscores',
        messageAr: 'اسم المستخدم يمكن أن يحتوي على أحرف وأرقام وشرطة سفلية فقط'
      });
    }

    // التحقق من كلمة المرور الحالية
    const adminUser = await validateAdminLogin(user.username, currentPassword);
    if (!adminUser) {
      return res.status(400).json({
        success: false,
        message: 'Current password is incorrect',
        messageAr: 'كلمة المرور الحالية غير صحيحة'
      });
    }

    // قراءة قاعدة البيانات وتحديث اسم المستخدم
    const database = JSON.parse(fs.readFileSync(DB_PATH, 'utf8'));
    
    if (!database.adminUser) {
      return res.status(500).json({
        success: false,
        message: 'Admin user not found in database',
        messageAr: 'المستخدم الإداري غير موجود في قاعدة البيانات'
      });
    }

    // تحديث اسم المستخدم
    database.adminUser.username = newUsername;
    database.adminUser.updatedAt = new Date().toISOString();

    // حفظ قاعدة البيانات
    fs.writeFileSync(DB_PATH, JSON.stringify(database, null, 2));

    res.status(200).json({
      success: true,
      message: 'Username changed successfully',
      messageAr: 'تم تغيير اسم المستخدم بنجاح',
      newUsername: newUsername
    });

  } catch (error) {
    console.error('Change username API error:', error);
    
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      messageAr: 'خطأ في الخادم الداخلي'
    });
  }
}
