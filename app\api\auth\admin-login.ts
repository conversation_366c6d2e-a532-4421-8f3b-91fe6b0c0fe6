import { NextApiRequest, NextApiResponse } from 'next';
import bcrypt from 'bcryptjs';
import { generateToken } from '../../../lib/auth';
import { executeQuerySingle } from '../../../lib/database-config';
import { serialize } from 'cookie';
import {
  checkRateLimit,
  RATE_LIMIT_CONFIGS,
  getClientIP,
  isIPBlocked,
  logSuspiciousActivity
} from '../../../lib/rate-limiter';

interface AdminUser {
  id: number;
  username: string;
  email: string;
  password_hash: string;
  is_active: boolean;
  last_login: Date | null;
  created_at: Date;
  updated_at: Date;
  deleted_at: Date | null;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    });
  }

  try {
    const clientIP = getClientIP(req);

    // فحص ما إذا كان IP محظوراً
    if (isIPBlocked(clientIP)) {
      logSuspiciousActivity(
        clientIP,
        '/api/auth/admin-login',
        'Blocked IP attempting login',
        req.headers['user-agent']
      );

      return res.status(403).json({
        success: false,
        message: 'Access denied. IP blocked due to suspicious activity.',
        messageAr: 'تم رفض الوصول. تم حظر عنوان IP بسبب نشاط مشبوه.'
      });
    }

    // فحص معدل الطلبات
    const rateLimitResult = checkRateLimit(req, 'login', RATE_LIMIT_CONFIGS.LOGIN);

    if (!rateLimitResult.allowed) {
      logSuspiciousActivity(
        clientIP,
        '/api/auth/admin-login',
        'Rate limit exceeded - possible brute force attack',
        req.headers['user-agent']
      );

      return res.status(429).json({
        success: false,
        message: 'Too many login attempts. Please try again later.',
        messageAr: 'محاولات تسجيل دخول كثيرة جداً. يرجى المحاولة لاحقاً.',
        retryAfter: rateLimitResult.retryAfter
      });
    }

    const { username, password } = req.body;

    console.log('🔐 محاولة تسجيل دخول:', { username, passwordLength: password?.length });

    // التحقق من وجود البيانات المطلوبة
    if (!username || !password) {
      console.log('❌ بيانات ناقصة');
      return res.status(400).json({
        success: false,
        message: 'Username and password are required',
        messageAr: 'اسم المستخدم وكلمة المرور مطلوبان'
      });
    }

    // التحقق من صحة البيانات
    if (typeof username !== 'string' || typeof password !== 'string') {
      return res.status(400).json({
        success: false,
        message: 'Invalid data format',
        messageAr: 'تنسيق البيانات غير صحيح'
      });
    }

    // التحقق من طول البيانات
    if (username.length > 50 || password.length > 100) {
      return res.status(400).json({
        success: false,
        message: 'Invalid data length',
        messageAr: 'طول البيانات غير صحيح'
      });
    }

    // البحث عن المستخدم في قاعدة البيانات
    console.log('🔍 البحث عن المستخدم في قاعدة البيانات...');
    const admin = await executeQuerySingle<AdminUser>(
      'SELECT * FROM admins WHERE username = ? AND is_active = 1 AND deleted_at IS NULL',
      [username.trim()]
    );

    console.log('👤 نتيجة البحث:', admin ? 'تم العثور على المستخدم' : 'لم يتم العثور على المستخدم');

    if (!admin) {
      console.log('❌ المستخدم غير موجود أو غير نشط');

      logSuspiciousActivity(
        clientIP,
        '/api/auth/admin-login',
        'Failed login attempt - invalid credentials',
        req.headers['user-agent']
      );

      return res.status(401).json({
        success: false,
        message: 'Invalid username or password',
        messageAr: 'اسم المستخدم أو كلمة المرور غير صحيحة'
      });
    }

    // التحقق من كلمة المرور
    console.log('🔐 التحقق من كلمة المرور...');
    const isValidPassword = await bcrypt.compare(password, admin.password_hash);
    console.log('🔑 نتيجة التحقق من كلمة المرور:', isValidPassword ? 'صحيحة' : 'خاطئة');

    if (!isValidPassword) {
      console.log('❌ كلمة المرور غير صحيحة');

      logSuspiciousActivity(
        clientIP,
        '/api/auth/admin-login',
        'Failed login attempt - invalid password',
        req.headers['user-agent']
      );

      return res.status(401).json({
        success: false,
        message: 'Invalid username or password',
        messageAr: 'اسم المستخدم أو كلمة المرور غير صحيحة'
      });
    }

    // تحديث وقت آخر تسجيل دخول
    console.log('📝 تحديث وقت آخر تسجيل دخول...');
    await executeQuerySingle(
      'UPDATE admins SET last_login = NOW(), updated_at = NOW() WHERE id = ?',
      [admin.id]
    );

    // إنشاء JWT token
    console.log('🎫 إنشاء JWT token...');
    const userForToken = {
      id: admin.id.toString(),
      username: admin.username,
      email: admin.email,
      role: 'admin',
      lastLogin: admin.last_login || new Date()
    };

    const token = generateToken(userForToken);

    // إعداد cookie آمن
    const cookieOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict' as const,
      maxAge: 24 * 60 * 60, // 24 ساعة
      path: '/'
    };

    const cookie = serialize('authToken', token, cookieOptions);
    res.setHeader('Set-Cookie', cookie);

    // نجح تسجيل الدخول - تسجيل النشاط الإيجابي
    console.log(`✅ Successful login for user: ${admin.username} from IP: ${clientIP}`);

    // إرجاع بيانات المستخدم بدون كلمة المرور
    return res.status(200).json({
      success: true,
      message: 'Login successful',
      messageAr: 'تم تسجيل الدخول بنجاح',
      user: {
        id: admin.id,
        username: admin.username,
        email: admin.email,
        role: 'admin'
      },
      token // إرسال التوكن أيضاً للاستخدام في الواجهة الأمامية
    });

  } catch (error) {
    console.error('Login API error:', error);

    const clientIP = getClientIP(req);
    logSuspiciousActivity(
      clientIP,
      '/api/auth/admin-login',
      'Server error during login attempt',
      req.headers['user-agent']
    );

    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      messageAr: 'خطأ في الخادم الداخلي'
    });
  }
}
