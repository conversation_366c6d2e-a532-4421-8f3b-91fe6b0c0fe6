import { NextApiRequest, NextApiResponse } from 'next';
import { generateToken } from '../../../lib/auth';
import { validateAdminLogin } from '../../../lib/secure-storage';
import { serialize } from 'cookie';
import {
  checkRateLimit,
  RATE_LIMIT_CONFIGS,
  getClientIP,
  isIPBlocked,
  logSuspiciousActivity
} from '../../../lib/rate-limiter';



export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // السماح فقط بطلبات POST
  if (req.method !== 'POST') {
    return res.status(405).json({ 
      success: false, 
      message: 'Method not allowed' 
    });
  }
  
  try {
    const clientIP = getClientIP(req);

    // فحص ما إذا كان IP محظوراً
    if (isIPBlocked(clientIP)) {
      logSuspiciousActivity(
        clientIP,
        '/api/auth/login',
        'Blocked IP attempting login',
        req.headers['user-agent']
      );

      return res.status(403).json({
        success: false,
        message: 'Access denied. IP blocked due to suspicious activity.',
        messageAr: 'تم رفض الوصول. تم حظر عنوان IP بسبب نشاط مشبوه.'
      });
    }

    // فحص معدل الطلبات
    const rateLimitResult = checkRateLimit(req, 'login', RATE_LIMIT_CONFIGS.LOGIN);

    if (!rateLimitResult.allowed) {
      logSuspiciousActivity(
        clientIP,
        '/api/auth/login',
        'Rate limit exceeded - possible brute force attack',
        req.headers['user-agent']
      );

      return res.status(429).json({
        success: false,
        message: 'Too many login attempts. Please try again later.',
        messageAr: 'محاولات تسجيل دخول كثيرة جداً. يرجى المحاولة لاحقاً.',
        retryAfter: rateLimitResult.retryAfter
      });
    }
    
    const { username, password } = req.body;
    
    // التحقق من وجود البيانات المطلوبة
    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: 'Username and password are required',
        messageAr: 'اسم المستخدم وكلمة المرور مطلوبان'
      });
    }

    // التحقق من صحة البيانات
    if (typeof username !== 'string' || typeof password !== 'string') {
      return res.status(400).json({
        success: false,
        message: 'Invalid data format',
        messageAr: 'تنسيق البيانات غير صحيح'
      });
    }

    // التحقق من طول البيانات
    if (username.length > 50 || password.length > 100) {
      return res.status(400).json({
        success: false,
        message: 'Invalid data length',
        messageAr: 'طول البيانات غير صحيح'
      });
    }
    
    // محاولة تسجيل الدخول
    const user = await validateAdminLogin(username.trim(), password);

    if (!user) {
      logSuspiciousActivity(
        clientIP,
        '/api/auth/login',
        'Failed login attempt - invalid credentials',
        req.headers['user-agent']
      );

      return res.status(401).json({
        success: false,
        message: 'Invalid username or password',
        messageAr: 'اسم المستخدم أو كلمة المرور غير صحيحة'
      });
    }

    // نجح تسجيل الدخول - تسجيل النشاط الإيجابي
    console.log(`✅ Successful login for user: ${user.username} from IP: ${clientIP}`);
    
    // إنشاء JWT token
    const token = generateToken(user);
    
    // إعداد cookie آمن
    const cookieOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict' as const,
      maxAge: 24 * 60 * 60, // 24 ساعة
      path: '/'
    };
    
    const cookie = serialize('authToken', token, cookieOptions);
    res.setHeader('Set-Cookie', cookie);
    
    // إرجاع الاستجابة
    res.status(200).json({
      success: true,
      message: 'Login successful',
      messageAr: 'تم تسجيل الدخول بنجاح',
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role
      },
      token // إرسال التوكن أيضاً للاستخدام في الواجهة الأمامية
    });
    
  } catch (error) {
    console.error('Login API error:', error);

    const clientIP = getClientIP(req);
    logSuspiciousActivity(
      clientIP,
      '/api/auth/login',
      'Server error during login attempt',
      req.headers['user-agent']
    );

    res.status(500).json({
      success: false,
      message: 'Internal server error',
      messageAr: 'خطأ في الخادم الداخلي'
    });
  }
}
