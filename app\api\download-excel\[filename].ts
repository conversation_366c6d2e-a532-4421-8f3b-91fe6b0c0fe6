import { NextApiRequest, NextApiResponse } from 'next';
import fs from 'fs';
import path from 'path';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).end(`Method ${req.method} Not Allowed`);
  }

  try {
    const { filename } = req.query;
    const excelDir = path.join(process.cwd(), 'public', 'uploads', 'excel');
    const filePath = path.join(excelDir, filename as string);

    // التحقق من وجود الملف
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        message: 'الملف غير موجود'
      });
    }

    // قراءة الملف
    const fileBuffer = fs.readFileSync(filePath);

    // تعيين headers للتحميل
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Length', fileBuffer.length.toString());

    // إرسال الملف
    res.send(fileBuffer);

  } catch (error) {
    console.error('Error downloading Excel file:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء تحميل الملف'
    });
  }
}
