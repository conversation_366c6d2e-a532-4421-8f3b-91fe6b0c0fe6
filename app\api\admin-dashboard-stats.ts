import { NextApiRequest, NextApiResponse } from 'next';
import mysql from 'mysql2/promise';
import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';

// التحقق من صحة JWT token
function verifyToken(token: string) {
  try {
    return jwt.verify(token, JWT_SECRET) as any;
  } catch (error) {
    return null;
  }
}

// استخراج token من الطلب
function extractToken(req: NextApiRequest): string | null {
  const authHeader = req.headers.authorization;
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }
  
  const tokenFromCookie = req.cookies.authToken;
  if (tokenFromCookie) {
    return tokenFromCookie;
  }
  
  return null;
}

// إعداد الاتصال بقاعدة البيانات
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'droobhajer_db',
  charset: 'utf8mb4'
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    });
  }

  // التحقق من المصادقة للوحة الإدارية
  const token = extractToken(req);
  if (!token) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required',
      messageAr: 'المصادقة مطلوبة'
    });
  }

  const decoded = verifyToken(token);
  if (!decoded) {
    return res.status(401).json({
      success: false,
      message: 'Invalid token',
      messageAr: 'رمز المصادقة غير صحيح'
    });
  }

  let connection;

  try {
    console.log('🔄 محاولة الاتصال بقاعدة البيانات...');
    console.log('📊 إعدادات قاعدة البيانات:', {
      host: dbConfig.host,
      user: dbConfig.user,
      database: dbConfig.database,
      hasPassword: !!dbConfig.password
    });

    // إنشاء اتصال بقاعدة البيانات
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // إحصائيات سريعة للمنتجات
    console.log('📦 جلب إحصائيات المنتجات...');
    const [quickProductsStats] = await connection.execute(`
      SELECT
        COUNT(*) as total,
        COUNT(CASE WHEN is_active = 1 THEN 1 END) as active,
        COUNT(CASE WHEN is_featured = 1 THEN 1 END) as featured,
        COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as added_this_week,
        COUNT(CASE WHEN updated_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as updated_today
      FROM products
    `);
    console.log('📦 إحصائيات المنتجات:', quickProductsStats[0]);

    // إحصائيات سريعة للفئات
    console.log('📁 جلب إحصائيات الفئات...');
    const [quickCategoriesStats] = await connection.execute(`
      SELECT
        COUNT(*) as total,
        COUNT(CASE WHEN is_active = 1 THEN 1 END) as active
      FROM categories
    `);
    console.log('📁 إحصائيات الفئات:', quickCategoriesStats[0]);

    // إحصائيات الفئات الفرعية
    console.log('📂 جلب إحصائيات الفئات الفرعية...');
    const [subcategoriesStats] = await connection.execute(`
      SELECT
        COUNT(*) as total_subcategories,
        COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_subcategories
      FROM subcategories
    `);
    console.log('📂 إحصائيات الفئات الفرعية:', subcategoriesStats[0]);

    // إحصائيات المستخدمين الإداريين
    const [adminStats] = await connection.execute(`
      SELECT 
        COUNT(*) as total_admins,
        COUNT(CASE WHEN last_login >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as active_this_week,
        COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as new_this_month
      FROM admins
    `);

    // أحدث المنتجات المضافة
    const [recentProducts] = await connection.execute(`
      SELECT 
        id,
        name_ar,
        name_en,
        is_active,
        is_featured,
        created_at
      FROM products 
      ORDER BY created_at DESC 
      LIMIT 5
    `);

    // إحصائيات طلبات التسعير من قاعدة البيانات
    const fs = require('fs');
    const path = require('path');

    let quoteStats = {
      total: 0,
      pending: 0,
      processed: 0,
      today: 0,
      this_week: 0
    };

    try {
      // جلب إحصائيات طلبات التسعير من قاعدة البيانات
      const [basicStats] = await connection.execute(`
        SELECT
          COUNT(*) as total,
          SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
          SUM(CASE WHEN status = 'processed' THEN 1 ELSE 0 END) as processed,
          SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected
        FROM quote_requests
        WHERE deleted_at IS NULL
      `);

      const [todayStats] = await connection.execute(`
        SELECT COUNT(*) as today_count
        FROM quote_requests
        WHERE deleted_at IS NULL
        AND DATE(created_at) = CURDATE()
      `);

      const [weekStats] = await connection.execute(`
        SELECT COUNT(*) as week_count
        FROM quote_requests
        WHERE deleted_at IS NULL
        AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
      `);

      quoteStats = {
        total: basicStats[0]?.total || 0,
        pending: basicStats[0]?.pending || 0,
        processed: basicStats[0]?.processed || 0,
        today: todayStats[0]?.today_count || 0,
        this_week: weekStats[0]?.week_count || 0
      };
    } catch (error) {
      console.log('Could not read quote requests from database:', error.message);
    }

    // إحصائيات النظام
    let systemStats = {
      database_size: 0,
      uploads_size: 0,
      total_files: 0
    };

    try {
      // حساب حجم مجلد الرفع
      const uploadsPath = path.join(process.cwd(), 'public', 'uploads');
      if (fs.existsSync(uploadsPath)) {
        const files = fs.readdirSync(uploadsPath);
        let totalSize = 0;
        
        files.forEach(file => {
          const filePath = path.join(uploadsPath, file);
          const stats = fs.statSync(filePath);
          totalSize += stats.size;
        });

        systemStats.uploads_size = Math.round((totalSize / (1024 * 1024)) * 100) / 100; // MB
        systemStats.total_files = files.length;
      }

      // حساب حجم قاعدة البيانات (تقريبي)
      const [dbSizeResult] = await connection.execute(`
        SELECT 
          ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
        FROM information_schema.tables 
        WHERE table_schema = ?
      `, [dbConfig.database]);

      systemStats.database_size = dbSizeResult[0]?.size_mb || 0;

    } catch (error) {
      console.log('Could not calculate system stats:', error.message);
    }

    // تجميع الإحصائيات
    const stats = {
      products: {
        total: quickProductsStats[0].total,
        active: quickProductsStats[0].active,
        featured: quickProductsStats[0].featured,
        added_this_week: quickProductsStats[0].added_this_week,
        updated_today: quickProductsStats[0].updated_today
      },
      categories: {
        total: quickCategoriesStats[0].total,
        active: quickCategoriesStats[0].active
      },
      subcategories: {
        total: subcategoriesStats[0].total_subcategories,
        active: subcategoriesStats[0].active_subcategories
      },
      admins: {
        total: adminStats[0].total_admins,
        active_this_week: adminStats[0].active_this_week,
        new_this_month: adminStats[0].new_this_month
      },
      quotes: quoteStats,
      system: systemStats,
      recent_products: recentProducts,
      last_updated: new Date().toISOString()
    };

    console.log('✅ تم جلب إحصائيات لوحة التحكم الإدارية بنجاح');
    console.log('📊 الإحصائيات النهائية:', {
      products: stats.products,
      categories: stats.categories,
      subcategories: stats.subcategories
    });

    res.status(200).json({
      success: true,
      stats
    });

  } catch (error) {
    console.error('❌ خطأ في جلب إحصائيات لوحة التحكم الإدارية:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch admin dashboard stats',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}
