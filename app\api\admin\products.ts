import { NextApiRequest, NextApiResponse } from 'next';
import {
  getProductsWithDetails,
  addProductWithDetails,
  updateProductWithDetails,
  deleteProductWithDetails,
  getProductWithDetails,
  getProductsByCategory,
  getProductsBySubcategory
} from '../../../lib/mysql-database';
import { requireAdminAuth } from '../../../lib/auth';
import { v4 as uuidv4 } from 'uuid';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // TODO: إعادة تفعيل المصادقة لاحقاً
  // const user = requireAdminAuth(req);
  // if (!user) {
  //   return res.status(401).json({
  //     success: false,
  //     message: 'Authentication required',
  //     messageAr: 'المصادقة مطلوبة'
  //   });
  // }

  try {
    switch (req.method) {
      case 'GET':
        const { categoryId, subcategoryId, id } = req.query;

        // إذا تم تمرير ID محدد، جلب منتج واحد مع التفاصيل
        if (id && typeof id === 'string') {
          const product = await getProductWithDetails(id);
          if (!product) {
            return res.status(404).json({
              success: false,
              message: 'Product not found',
              messageAr: 'المنتج غير موجود'
            });
          }
          return res.status(200).json({ success: true, data: product });
        }

        // جلب المنتجات حسب الفئة أو الفئة الفرعية أو جميع المنتجات
        let products;
        if (subcategoryId && typeof subcategoryId === 'string') {
          products = await getProductsBySubcategory(subcategoryId);
        } else if (categoryId && typeof categoryId === 'string') {
          products = await getProductsByCategory(categoryId);
        } else {
          products = await getProductsWithDetails();
        }

        res.status(200).json({ success: true, data: products });
        break;

      case 'POST':
        const {
          title,
          titleAr,
          description,
          descriptionAr,
          images,
          price,
          originalPrice,
          available,
          categoryId: catId,
          subcategoryId: subCatId,
          features,
          featuresAr,
          specifications,
          isActive,
          isFeatured
        } = req.body;

        if (!title || !titleAr || !description || !descriptionAr || !catId || !subCatId || price === undefined) {
          return res.status(400).json({
            success: false,
            message: 'Required fields are missing',
            messageAr: 'الحقول المطلوبة مفقودة'
          });
        }

        // تحضير بيانات المنتج
        const productId = uuidv4();
        const productData = {
          product: {
            id: productId,
            title,
            title_ar: titleAr,
            description,
            description_ar: descriptionAr,
            price: parseFloat(price),
            original_price: originalPrice ? parseFloat(originalPrice) : null,
            is_available: available !== undefined ? available : true,
            category_id: catId,
            subcategory_id: subCatId,
            is_active: isActive !== undefined ? isActive : true,
            is_featured: isFeatured !== undefined ? isFeatured : false
          },
          images: images && Array.isArray(images) ? images.filter(img => img && img.trim()) : [],
          features: [],
          specifications: []
        };

        // تحضير المميزات
        if (features && featuresAr && Array.isArray(features) && Array.isArray(featuresAr)) {
          for (let i = 0; i < Math.min(features.length, featuresAr.length); i++) {
            if (features[i] && featuresAr[i] && features[i].trim() && featuresAr[i].trim()) {
              productData.features.push({
                text: features[i].trim(),
                textAr: featuresAr[i].trim()
              });
            }
          }
        }

        // تحضير المواصفات
        if (specifications && Array.isArray(specifications)) {
          for (const spec of specifications) {
            if (spec.nameEn && spec.nameAr && spec.valueEn && spec.valueAr &&
                spec.nameEn.trim() && spec.nameAr.trim() && spec.valueEn.trim() && spec.valueAr.trim()) {
              productData.specifications.push({
                key: spec.nameEn.trim(),
                keyAr: spec.nameAr.trim(),
                value: spec.valueEn.trim(),
                valueAr: spec.valueAr.trim()
              });
            }
          }
        }

        const newProduct = await addProductWithDetails(productData);
        res.status(201).json({ success: true, data: newProduct });
        break;

      case 'PUT':
        const { id: updateId } = req.query;

        if (!updateId || typeof updateId !== 'string') {
          return res.status(400).json({
            success: false,
            message: 'Product ID is required',
            messageAr: 'معرف المنتج مطلوب'
          });
        }

        const existingProduct = await getProductWithDetails(updateId);
        if (!existingProduct) {
          return res.status(404).json({
            success: false,
            message: 'Product not found',
            messageAr: 'المنتج غير موجود'
          });
        }

        const {
          title: updateTitle,
          titleAr: updateTitleAr,
          description: updateDescription,
          descriptionAr: updateDescriptionAr,
          images: updateImages,
          price: updatePrice,
          originalPrice: updateOriginalPrice,
          available: updateAvailable,
          categoryId: updateCatId,
          subcategoryId: updateSubCatId,
          features: updateFeatures,
          featuresAr: updateFeaturesAr,
          specifications: updateSpecifications,
          isActive: updateIsActive,
          isFeatured: updateIsFeatured
        } = req.body;

        // تحضير بيانات التحديث
        const updateData: any = {};

        // تحديث بيانات المنتج الأساسية
        if (updateTitle !== undefined || updateTitleAr !== undefined || updateDescription !== undefined ||
            updateDescriptionAr !== undefined || updatePrice !== undefined || updateOriginalPrice !== undefined ||
            updateAvailable !== undefined || updateCatId !== undefined || updateSubCatId !== undefined ||
            updateIsActive !== undefined || updateIsFeatured !== undefined) {

          updateData.product = {};

          if (updateTitle !== undefined) updateData.product.title = updateTitle;
          if (updateTitleAr !== undefined) updateData.product.title_ar = updateTitleAr;
          if (updateDescription !== undefined) updateData.product.description = updateDescription;
          if (updateDescriptionAr !== undefined) updateData.product.description_ar = updateDescriptionAr;
          if (updatePrice !== undefined) updateData.product.price = parseFloat(updatePrice);
          if (updateOriginalPrice !== undefined) updateData.product.original_price = updateOriginalPrice ? parseFloat(updateOriginalPrice) : null;
          if (updateAvailable !== undefined) updateData.product.is_available = updateAvailable;
          if (updateCatId !== undefined) updateData.product.category_id = updateCatId;
          if (updateSubCatId !== undefined) updateData.product.subcategory_id = updateSubCatId;
          if (updateIsActive !== undefined) updateData.product.is_active = updateIsActive;
          if (updateIsFeatured !== undefined) updateData.product.is_featured = updateIsFeatured;
        }

        // تحديث الصور
        if (updateImages !== undefined && Array.isArray(updateImages)) {
          updateData.images = updateImages.filter(img => img && img.trim());
        }

        // تحديث المميزات
        if (updateFeatures !== undefined && updateFeaturesAr !== undefined &&
            Array.isArray(updateFeatures) && Array.isArray(updateFeaturesAr)) {
          updateData.features = [];
          for (let i = 0; i < Math.min(updateFeatures.length, updateFeaturesAr.length); i++) {
            if (updateFeatures[i] && updateFeaturesAr[i] && updateFeatures[i].trim() && updateFeaturesAr[i].trim()) {
              updateData.features.push({
                text: updateFeatures[i].trim(),
                textAr: updateFeaturesAr[i].trim()
              });
            }
          }
        }

        // تحديث المواصفات
        if (updateSpecifications !== undefined && Array.isArray(updateSpecifications)) {
          updateData.specifications = [];
          for (const spec of updateSpecifications) {
            if (spec.nameEn && spec.nameAr && spec.valueEn && spec.valueAr &&
                spec.nameEn.trim() && spec.nameAr.trim() && spec.valueEn.trim() && spec.valueAr.trim()) {
              updateData.specifications.push({
                key: spec.nameEn.trim(),
                keyAr: spec.nameAr.trim(),
                value: spec.valueEn.trim(),
                valueAr: spec.valueAr.trim()
              });
            }
          }
        }

        const updatedProduct = await updateProductWithDetails(updateId, updateData);
        res.status(200).json({ success: true, data: updatedProduct });
        break;

      case 'DELETE':
        const { id: deleteId } = req.query;

        if (!deleteId || typeof deleteId !== 'string') {
          return res.status(400).json({
            success: false,
            message: 'Product ID is required',
            messageAr: 'معرف المنتج مطلوب'
          });
        }

        const deleted = await deleteProductWithDetails(deleteId);

        if (!deleted) {
          return res.status(404).json({
            success: false,
            message: 'Product not found',
            messageAr: 'المنتج غير موجود'
          });
        }

        res.status(200).json({
          success: true,
          message: 'Product deleted successfully',
          messageAr: 'تم حذف المنتج بنجاح'
        });
        break;

      default:
        res.setHeader('Allow', ['GET', 'POST', 'PUT', 'DELETE']);
        res.status(405).json({
          success: false,
          message: `Method ${req.method} not allowed`,
          messageAr: `الطريقة ${req.method} غير مسموحة`
        });
    }
  } catch (error) {
    console.error('Products API error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      messageAr: 'خطأ داخلي في الخادم',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}
