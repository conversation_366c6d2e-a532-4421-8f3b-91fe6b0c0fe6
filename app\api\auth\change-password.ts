import { NextApiRequest, NextApiResponse } from 'next';
import { requireAdminAuth } from '../../../lib/auth';
import { validateAdminLogin } from '../../../lib/secure-storage';
import bcrypt from 'bcryptjs';
import fs from 'fs';
import path from 'path';

// مسار ملف قاعدة البيانات
const DB_PATH = path.join(process.cwd(), 'src/data/database.json');

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // السماح فقط بطلبات POST
  if (req.method !== 'POST') {
    return res.status(405).json({ 
      success: false, 
      message: 'Method not allowed' 
    });
  }

  try {
    // التحقق من المصادقة
    const user = requireAdminAuth(req);
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required',
        messageAr: 'المصادقة مطلوبة'
      });
    }

    const { currentPassword, newPassword } = req.body;

    // التحقق من وجود البيانات المطلوبة
    if (!currentPassword || !newPassword) {
      return res.status(400).json({
        success: false,
        message: 'Current password and new password are required',
        messageAr: 'كلمة المرور الحالية والجديدة مطلوبتان'
      });
    }

    // التحقق من قوة كلمة المرور الجديدة
    if (newPassword.length < 8) {
      return res.status(400).json({
        success: false,
        message: 'New password must be at least 8 characters long',
        messageAr: 'كلمة المرور الجديدة يجب أن تكون 8 أحرف على الأقل'
      });
    }

    // التحقق من كلمة المرور الحالية
    const adminUser = await validateAdminLogin(user.username, currentPassword);
    if (!adminUser) {
      return res.status(400).json({
        success: false,
        message: 'Current password is incorrect',
        messageAr: 'كلمة المرور الحالية غير صحيحة'
      });
    }

    // تشفير كلمة المرور الجديدة
    const saltRounds = 12;
    const hashedNewPassword = await bcrypt.hash(newPassword, saltRounds);

    // قراءة قاعدة البيانات وتحديث كلمة المرور
    const database = JSON.parse(fs.readFileSync(DB_PATH, 'utf8'));
    
    // إضافة بيانات المستخدم الإداري إذا لم تكن موجودة
    if (!database.adminUser) {
      database.adminUser = {
        id: '1',
        username: 'admin',
        email: '<EMAIL>',
        role: 'admin',
        lastLogin: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
    }

    // تحديث كلمة المرور
    database.adminUser.password = hashedNewPassword;
    database.adminUser.updatedAt = new Date().toISOString();

    // حفظ قاعدة البيانات
    fs.writeFileSync(DB_PATH, JSON.stringify(database, null, 2));

    res.status(200).json({
      success: true,
      message: 'Password changed successfully',
      messageAr: 'تم تغيير كلمة المرور بنجاح'
    });

  } catch (error) {
    console.error('Change password API error:', error);
    
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      messageAr: 'خطأ في الخادم الداخلي'
    });
  }
}
