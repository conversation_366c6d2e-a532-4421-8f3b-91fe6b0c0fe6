import { NextApiRequest, NextApiResponse } from 'next';
import {
  getSubcategories,
  addSubcategory,
  updateSubcategory,
  deleteSubcategory,
  getSubcategoryById,
  getSubcategoriesByCategory
} from '../../../../lib/mysql-database';
import { requireAdminAuth } from '../../../../lib/auth';
import { v4 as uuidv4 } from 'uuid';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // TODO: إعادة تفعيل المصادقة لاحقاً
  // const user = requireAdminAuth(req);
  // if (!user) {
  //   return res.status(401).json({
  //     success: false,
  //     message: 'Authentication required',
  //     messageAr: 'المصادقة مطلوبة'
  //   });
  // }

  try {
    switch (req.method) {
      case 'GET':
        const { categoryId } = req.query;

        let subcategories;
        if (categoryId && typeof categoryId === 'string') {
          subcategories = await getSubcategoriesByCategory(categoryId);
        } else {
          subcategories = await getSubcategories();
        }

        res.status(200).json({ success: true, data: subcategories });
        break;

      case 'POST':
        const { name, nameAr, categoryId: catId, description, descriptionAr, image, isActive } = req.body;

        if (!name || !nameAr || !catId) {
          return res.status(400).json({
            success: false,
            message: 'Name, Arabic name, and category ID are required',
            messageAr: 'الاسم والاسم بالعربية ومعرف الفئة مطلوبة'
          });
        }

        const subcategoryData = {
          id: uuidv4(),
          name,
          name_ar: nameAr,
          category_id: catId,
          description: description || null,
          description_ar: descriptionAr || null,
          image: image || null,
          is_active: isActive !== undefined ? isActive : true
        };

        const newSubcategory = await addSubcategory(subcategoryData);
        res.status(201).json({ success: true, data: newSubcategory });
        break;

      case 'PUT':
        const { id: updateId } = req.query;

        if (!updateId || typeof updateId !== 'string') {
          return res.status(400).json({
            success: false,
            message: 'Subcategory ID is required',
            messageAr: 'معرف الفئة الفرعية مطلوب'
          });
        }

        const existingSubcategory = await getSubcategoryById(updateId);
        if (!existingSubcategory) {
          return res.status(404).json({
            success: false,
            message: 'Subcategory not found',
            messageAr: 'الفئة الفرعية غير موجودة'
          });
        }

        const updatedSubcategory = await updateSubcategory(updateId, req.body);
        res.status(200).json({ success: true, data: updatedSubcategory });
        break;

      case 'DELETE':
        const { id: deleteId } = req.query;

        if (!deleteId || typeof deleteId !== 'string') {
          return res.status(400).json({
            success: false,
            message: 'Subcategory ID is required',
            messageAr: 'معرف الفئة الفرعية مطلوب'
          });
        }

        const deleted = await deleteSubcategory(deleteId);

        if (!deleted) {
          return res.status(404).json({
            success: false,
            message: 'Subcategory not found',
            messageAr: 'الفئة الفرعية غير موجودة'
          });
        }

        res.status(200).json({
          success: true,
          message: 'Subcategory deleted successfully',
          messageAr: 'تم حذف الفئة الفرعية بنجاح'
        });
        break;

      default:
        res.setHeader('Allow', ['GET', 'POST', 'PUT', 'DELETE']);
        res.status(405).json({ 
          success: false, 
          message: `Method ${req.method} not allowed` 
        });
    }
  } catch (error) {
    console.error('Subcategories API error:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Internal server error' 
    });
  }
}
