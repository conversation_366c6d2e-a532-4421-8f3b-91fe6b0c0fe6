import { NextApiRequest, NextApiResponse } from 'next';
import fs from 'fs';
import path from 'path';

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const { path: imagePath } = req.query;
    
    if (!imagePath || !Array.isArray(imagePath)) {
      return res.status(400).json({ message: 'Invalid path' });
    }

    // بناء مسار الملف
    const fileName = imagePath.join('/');
    const filePath = path.join(process.cwd(), 'public', 'uploads', fileName);

    // التحقق من وجود الملف
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ message: 'Image not found' });
    }

    // قراءة الملف
    const fileBuffer = fs.readFileSync(filePath);
    
    // تحديد نوع المحتوى بناءً على امتداد الملف
    const ext = path.extname(fileName).toLowerCase();
    let contentType = 'image/jpeg'; // افتراضي
    
    switch (ext) {
      case '.png':
        contentType = 'image/png';
        break;
      case '.jpg':
      case '.jpeg':
        contentType = 'image/jpeg';
        break;
      case '.gif':
        contentType = 'image/gif';
        break;
      case '.webp':
        contentType = 'image/webp';
        break;
      case '.svg':
        contentType = 'image/svg+xml';
        break;
    }

    // تعيين headers للتخزين المؤقت
    res.setHeader('Content-Type', contentType);
    res.setHeader('Cache-Control', 'public, max-age=31536000, immutable');
    res.setHeader('Content-Length', fileBuffer.length);

    // إرسال الصورة
    res.status(200).send(fileBuffer);
    
  } catch (error) {
    console.error('Error serving image:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
}
