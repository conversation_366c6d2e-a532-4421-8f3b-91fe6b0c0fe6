import { NextApiRequest, NextApiResponse } from 'next';
import { requireAuth } from '../../../lib/auth';
import { getAdminUser } from '../../../lib/secure-storage';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // السماح فقط بطلبات GET
  if (req.method !== 'GET') {
    return res.status(405).json({ 
      success: false, 
      message: 'Method not allowed' 
    });
  }
  
  try {
    // التحقق من المصادقة
    const tokenPayload = requireAuth(req);
    
    if (!tokenPayload) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required',
        messageAr: 'المصادقة مطلوبة'
      });
    }
    
    // الحصول على بيانات المستخدم الحديثة
    const adminData = await getAdminUser();
    
    // التحقق من أن المستخدم ما زال موجوداً وصالحاً
    if (adminData.id !== tokenPayload.userId || adminData.username !== tokenPayload.username) {
      return res.status(401).json({
        success: false,
        message: 'Invalid token',
        messageAr: 'رمز المصادقة غير صالح'
      });
    }
    
    // إرجاع بيانات المستخدم
    res.status(200).json({
      success: true,
      user: {
        id: adminData.id,
        username: adminData.username,
        email: adminData.email,
        role: adminData.role,
        lastLogin: adminData.lastLogin
      }
    });
    
  } catch (error) {
    console.error('Verify API error:', error);
    
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      messageAr: 'خطأ في الخادم الداخلي'
    });
  }
}
